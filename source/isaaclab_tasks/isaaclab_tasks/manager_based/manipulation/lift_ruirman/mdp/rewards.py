# Copyright (c) 2022-2025, The Isaac Lab Project Developers (https://github.com/isaac-sim/IsaacLab/blob/main/CONTRIBUTORS.md).
# All rights reserved.
#
# SPDX-License-Identifier: BSD-3-Clause

from __future__ import annotations

import torch
from typing import TYPE_CHECKING

from isaaclab.assets import RigidObject
from isaaclab.managers import SceneEntityCfg
from isaaclab.sensors import FrameTransformer
from isaaclab.utils.math import combine_frame_transforms
from loguru import logger

if TYPE_CHECKING:
    from isaaclab.envs import ManagerBasedRLEnv


def object_is_lifted(
    env: ManagerBasedRLEnv, minimal_height: float, object_cfg: SceneEntityCfg = SceneEntityCfg("object")
) -> torch.Tensor:
    """Reward the agent for lifting the object above the minimal height."""
    object: RigidObject = env.scene[object_cfg.name]
    return torch.where(object.data.root_pos_w[:, 2] > minimal_height, 1.0, 0.0)


def object_ee_distance(
    env: ManagerBasedRLEnv,
    std: float,
    object_cfg: SceneEntityCfg = SceneEntityCfg("object"),
    ee_frame_cfg: SceneEntityCfg = SceneEntityCfg("ee_frame"),
) -> torch.Tensor:
    """Reward the agent for reaching the object using tanh-kernel."""
    # extract the used quantities (to enable type-hinting)
    object: RigidObject = env.scene[object_cfg.name]
    ee_frame: FrameTransformer = env.scene[ee_frame_cfg.name]
    # Target object position: (num_envs, 3)
    cube_pos_w = object.data.root_pos_w
    # End-effector position: (num_envs, 3)
    ee_w = ee_frame.data.target_pos_w[..., 0, :]
    # Distance of the end-effector to the object: (num_envs,)
    object_ee_distance = torch.norm(cube_pos_w - ee_w, dim=1)
    
    # logger.info(f"object_ee_distance:, {object_ee_distance[0]}")

    return 1 - torch.tanh(object_ee_distance / std)


def object_ee_distance_yoz_plane(
    env: ManagerBasedRLEnv,
    std: float,
    x_tolerance: float = 0.05,
    object_cfg: SceneEntityCfg = SceneEntityCfg("object"),
    ee_frame_cfg: SceneEntityCfg = SceneEntityCfg("ee_frame"),
) -> torch.Tensor:
    """Reward the agent for reaching the object in yOz plane using tanh-kernel.

    This reward function only considers the distance in the yOz plane (ignoring x-axis)
    and only provides reward when the x-axis difference is within the specified tolerance.

    Args:
        env: The RL environment.
        std: Standard deviation for the tanh kernel.
        x_tolerance: Maximum allowed difference in x-axis for the reward to be active.
        object_cfg: Configuration for the object entity.
        ee_frame_cfg: Configuration for the end-effector frame entity.

    Returns:
        Reward tensor of shape (num_envs,).
    """
    # extract the used quantities (to enable type-hinting)
    object: RigidObject = env.scene[object_cfg.name]
    ee_frame: FrameTransformer = env.scene[ee_frame_cfg.name]

    # Target object position: (num_envs, 3)
    cube_pos_w = object.data.root_pos_w
    # End-effector position: (num_envs, 3)
    ee_w = ee_frame.data.target_pos_w[..., 0, :]

    # Calculate position differences
    pos_diff = cube_pos_w - ee_w  # (num_envs, 3)

    # Check if x-axis difference is within tolerance
    x_diff = torch.abs(pos_diff[:, 0])  # (num_envs,)
    x_within_tolerance = x_diff < x_tolerance  # (num_envs,)

    # Calculate distance in yOz plane only (ignoring x-axis)
    yoz_distance = torch.norm(pos_diff[:, 1:3], dim=1)  # (num_envs,)

    # Calculate reward using tanh kernel
    yoz_reward = 1 - torch.tanh(yoz_distance / std)

    # Apply reward only when x is within tolerance
    reward = torch.where(x_within_tolerance, yoz_reward, 0.0)

    # logger.info(f"x_diff: {x_diff[0]:.4f}, yoz_distance: {yoz_distance[0]:.4f}, reward: {reward[0]:.4f}")

    return reward


def object_goal_distance(
    env: ManagerBasedRLEnv,
    std: float,
    minimal_height: float,
    command_name: str,
    robot_cfg: SceneEntityCfg = SceneEntityCfg("robot"),
    object_cfg: SceneEntityCfg = SceneEntityCfg("object"),
) -> torch.Tensor:
    """Reward the agent for tracking the goal pose using tanh-kernel."""
    # extract the used quantities (to enable type-hinting)
    robot: RigidObject = env.scene[robot_cfg.name]
    object: RigidObject = env.scene[object_cfg.name]
    command = env.command_manager.get_command(command_name)
    # compute the desired position in the world frame
    des_pos_b = command[:, :3]
    des_pos_w, _ = combine_frame_transforms(robot.data.root_pos_w, robot.data.root_quat_w, des_pos_b)
    # distance of the end-effector to the object: (num_envs,)
    distance = torch.norm(des_pos_w - object.data.root_pos_w, dim=1)

    # logger.info(f"joint_pos: {robot.data.joint_pos[:, :]}")

    # rewarded if the object is lifted above the threshold
    return (object.data.root_pos_w[:, 2] > minimal_height) * (1 - torch.tanh(distance / std))
