cd ~/IsaacLab

# ./isaaclab.sh -p scripts/reinforcement_learning/skrl/play.py \
# --task <PERSON>-<PERSON><PERSON>-<PERSON><PERSON>-Franka-v0 \
# --num_envs 1 \
# --checkpoint logs/skrl/franka_lift/2025-06-12_17-28-34_ppo_torch/checkpoints/agent_36000.pt 

./isaaclab.sh -p scripts/reinforcement_learning/skrl/play.py \
--task <PERSON>-<PERSON><PERSON>-<PERSON><PERSON>-Ruirman-v0 \
--num_envs 9 \
--checkpoint logs/skrl/ruirman_lift/2025-06-25_16-50-22_ppo_torch/checkpoints/agent_50000.pt 
