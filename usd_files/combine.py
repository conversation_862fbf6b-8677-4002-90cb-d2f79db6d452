from omni.isaac.core import World
from omni.isaac.core.objects import Articulation
from omni.isaac.core.utils.prims import create_prim, set_parent
from omni.isaac.core.utils.transformations import tf_matrix_from_pos_rot
import carb
import omni.usd
import numpy as np
from pxr import UsdGeom, Gf
# 创建世界
world = World()
# 加载 RM75（例子：放在某路径）
rm75_path = "/World/Robot/RM75"
rm75_usd_path = "rm75/rm75.usd"   # 用你的路径替换
create_prim(rm75_path, "Xform", usd_path=rm75_usd_path)
# 加载 Panda Hand：放在临时路径中
panda_hand_path = "/World/Robot/PandaHand"
panda_hand_usd_path = "panda_hand.usd"
create_prim(panda_hand_path, "Xform", usd_path=panda_hand_usd_path)

set_parent(child_path=panda_hand_path, parent_path=rm75_path + "/Link7")

# 设置 Panda Hand 在末端的相对位姿（根据视觉调整）
ropa = Gf.Vec3d(0.0, 0.0, 0.05)  # 相对于 tool0 的位移
rotation = Gf.Quatd(1.0, 0.0, 0.0, 0.0)  # 用单位四元数或使用 Gf.Rotation
# 应用变换
stage = omni.usd.get_context().get_stage()
xform = UsdGeom.Xformable(stage.GetPrimAtPath(panda_hand_path))
xform.AddTranslateOp().Set(ropa)
xform.AddOrientOp().Set(rotation)

usd_path_out = "rm75_with_panda_hand.usd"
omni.usd.get_context().save_as_stage(usd_path_out)
print("组合模型保存到了:", usd_path_out)